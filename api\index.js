const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const db = require('./db');
const session = require('express-session');
const multer = require('multer');
const path = require('path');
const { createServer } = require('http');
const { Server } = require('socket.io');
const { generalLimiter, testLimiter } = require('./middleware/rateLimiter');

// Import API endpoints
const apiEndpoints = require('./api-endpoints');

require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Trust proxy for IP address detection (disabled for development)
// app.set('trust proxy', true);

// Create HTTP server and Socket.IO
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
    credentials: true
  }
});

// WebSocket connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Make io available to other modules
app.set('io', io);

// Security Middleware
app.use(helmet()); // Set security headers
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  credentials: true
}));
app.use(generalLimiter); // Apply general rate limiting to all requests
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false,
  saveUninitialized: true,
  cookie: {
    maxAge: 1000 * 60 * 60 * 24 // 24 hours
    //secure: true; use if using https
  }
}));

// Body parsing middleware with size limits
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Multer configuration for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 10 // Max 10 files per request
  },
  fileFilter: (req, file, cb) => {
    // Check if file is an image
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

// Make upload middleware available to api-endpoints
app.set('upload', upload);

// Serve static files from uploads directory
app.use('/uploads', express.static(path.join(__dirname, '..', 'public', 'uploads')));

// Test database connection
app.get('/api/test-db', testLimiter, async (req, res) => {
  try {
    const [rows] = await db.execute('SELECT 1 as test');
    res.json({
      success: true,
      message: 'Database connection successful',
      data: rows
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Database connection failed',
      details: error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    service: 'GG Catalog API'
  });
});

// Root route with HTML view
app.get('/', (req, res) => {
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Server</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.2);
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .server-icon {
            margin-bottom: 1.5rem;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .title {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .status {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
            padding: 1.2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 14px;
            height: 14px;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { 
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
                transform: scale(1);
            }
            50% { 
                transform: scale(1.1);
            }
            70% { 
                box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
            }
            100% { 
                box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
                transform: scale(1);
            }
        }

        .info {
            color: #666;
            font-size: 0.95rem;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #eee;
        }

        .timestamp {
            font-family: 'Courier New', monospace;
            color: #999;
            font-size: 0.9rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="server-icon">
            <svg width="80" height="80" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="10" y="20" width="80" height="15" rx="3" fill="#1e3c72" opacity="0.8"/>
                <rect x="10" y="40" width="80" height="15" rx="3" fill="#2a5298" opacity="0.8"/>
                <rect x="10" y="60" width="80" height="15" rx="3" fill="#1e3c72" opacity="0.8"/>
                
                <circle cx="20" cy="27.5" r="2" fill="#4caf50"/>
                <circle cx="20" cy="47.5" r="2" fill="#4caf50"/>
                <circle cx="20" cy="67.5" r="2" fill="#4caf50"/>
                
                <rect x="30" y="25" width="25" height="5" rx="1" fill="rgba(255,255,255,0.3)"/>
                <rect x="30" y="45" width="35" height="5" rx="1" fill="rgba(255,255,255,0.3)"/>
                <rect x="30" y="65" width="20" height="5" rx="1" fill="rgba(255,255,255,0.3)"/>
                
                <path d="M75 25 L85 30 L75 35 Z" fill="#4caf50"/>
                <path d="M75 45 L85 50 L75 55 Z" fill="#4caf50"/>
                <path d="M75 65 L85 70 L75 75 Z" fill="#4caf50"/>
            </svg>
        </div>
        
        <div class="title">API Server</div>
        <div class="subtitle">Service Running Successfully</div>
        
        <div class="status">
            <span class="status-indicator"></span>
            Server Online - Port ${PORT}
        </div>

        <div class="info">
            <p>System Status: <strong style="color: #4caf50;">Active</strong></p>
            <p class="timestamp">Started: ${new Date().toLocaleString()}</p>
        </div>
    </div>
</body>
</html>
  `;
  res.send(html);
});

// Use API endpoints
app.use('/api', apiEndpoints);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        error: 'File size too large. Maximum size is 10MB.'
      });
    }
    if (err.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        error: 'Too many files. Maximum is 10 files per request.'
      });
    }
  }

  if (err.message === 'Only image files are allowed') {
    return res.status(400).json({
      success: false,
      error: 'Only image files are allowed.'
    });
  }

  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler for undefined routes
app.use('*', (req, res) => {
  if (req.originalUrl.startsWith('/api/')) {
    res.status(404).json({
      success: false,
      error: 'API endpoint not found',
      path: req.originalUrl
    });
  } else {
    res.status(404).send(`
      <html>
        <head><title>404 - Page Not Found</title></head>
        <body style="font-family: Arial, sans-serif; text-align: center; margin-top: 50px;">
          <h1>404 - Page Not Found</h1>
          <p>The page you're looking for doesn't exist.</p>
          <a href="/" style="color: #667eea; text-decoration: none;">← Back to API Home</a>
        </body>
      </html>
    `);
  }
});

// Start server
server.listen(PORT, () => {
  console.log(`🚀 GG Catalog API Server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔍 Database test: http://localhost:${PORT}/api/test-db`);
  console.log(`🏠 API Home: http://localhost:${PORT}/`);
  console.log(`🔌 WebSocket server ready for connections`);
});

module.exports = app;