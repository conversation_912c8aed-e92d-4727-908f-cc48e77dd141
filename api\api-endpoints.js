const express = require('express');
const router = express.Router();

const {
  strictLimiter,
  adminLimiter,
  adminLoginLimiter,
  ratingLimiter
} = require('./middleware/rateLimiter');

const {
  handleValidationErrors,
  validateId,
  validateProductId,
  validateAdmin,
  validateBrand,
  validateCategory,
  validateProduct,
  validateVariant,
  validatePhoto,
  validateRating,
  validateBanner
} = require('./middleware/validation');

const { 
  requireAdmin, 
  optionalAdmin, 
  requireAdminStrict, 
  requireAdminSafe 
} = require('./middleware/auth');

// Import CRUD modules
const adminCRUD = require('./database/adminCRUD');
const adminSession = require('./database/adminSession');
const brandCRUD = require('./database/brandCRUD');
const categoryCRUD = require('./database/categoryCRUD');
const productCRUD = require('./database/productCRUD');
const variantCRUD = require('./database/variantCRUD');
const photoCRUD = require('./database/photoCRUD');
const ratingCRUD = require('./database/ratingCRUD');
const bannerCRUD = require('./database/bannerCRUD');

// Middleware to get upload from app
router.use((req, res, next) => {
  req.upload = req.app.get('upload');
  next();
});

// ===== ADMIN ROUTES =====

// Admin login
router.post('/admin/login', adminLoginLimiter, adminSession.adminLogin);

// Admin logout
router.post('/admin/logout', adminLimiter, adminSession.adminLogout);

// Admin session check
router.get('/admin/session', adminLimiter, adminSession.checkSession);

// Get all admins (admin only)
router.get('/admins', adminLimiter, requireAdmin, adminCRUD.getAllAdmins);

// Create new admin (admin only)
router.post('/admins', adminLimiter, requireAdmin, validateAdmin, handleValidationErrors, adminCRUD.createAdmin);

// Update admin (admin only)
router.put('/admins/:id', adminLimiter, requireAdmin, validateAdmin, handleValidationErrors, adminCRUD.updateAdmin);

// Delete admin (admin only)
router.delete('/admins/:id', adminLimiter, requireAdmin, adminCRUD.deleteAdmin);

// ===== BRAND ROUTES =====

// Get all brands (public)
router.get('/brands', brandCRUD.getAllBrands);

// Get brand by ID (public)
router.get('/brands/:id', validateId, handleValidationErrors, brandCRUD.getBrandById);

// Create new brand (admin only)
router.post('/brands', strictLimiter, requireAdmin, validateBrand, handleValidationErrors, brandCRUD.createBrand);

// Update brand (admin only)
router.put('/brands/:id', strictLimiter, requireAdmin, validateId, validateBrand, handleValidationErrors, brandCRUD.updateBrand);

// Delete brand (admin only)
router.delete('/brands/:id', strictLimiter, requireAdmin, validateId, handleValidationErrors, brandCRUD.deleteBrand);

// Upload brand image (admin only)
router.post('/brands/:id/upload-image', strictLimiter, requireAdmin, (req, res, next) => {
  req.upload.single('image')(req, res, next);
}, brandCRUD.uploadBrandImage);

// ===== CATEGORY ROUTES =====

// Get all categories (public)
router.get('/categories', categoryCRUD.getAllCategories);

// Get category by ID (public)
router.get('/categories/:id', validateId, handleValidationErrors, categoryCRUD.getCategoryById);

// Create new category (admin only)
router.post('/categories', strictLimiter, requireAdmin, validateCategory, handleValidationErrors, categoryCRUD.createCategory);

// Update category (admin only)
router.put('/categories/:id', strictLimiter, requireAdmin, validateId, validateCategory, handleValidationErrors, categoryCRUD.updateCategory);

// Delete category (admin only)
router.delete('/categories/:id', strictLimiter, requireAdmin, validateId, handleValidationErrors, categoryCRUD.deleteCategory);

// Upload category image (admin only)
router.post('/categories/:id/upload-image', strictLimiter, requireAdmin, (req, res, next) => {
  req.upload.single('image')(req, res, next);
}, categoryCRUD.uploadCategoryImage);

// ===== PRODUCT ROUTES =====

// Get all products with brand and category info (public)
router.get('/products', productCRUD.getAllProducts);

// Search products by name (public) - must be before :id route
router.get('/products/search', productCRUD.searchProducts);

// Get High Sales Product (public)
router.get('/products/high-sales', productCRUD.getHighSalesProducts);

// Get Best Product (public)
router.get('/products/best', productCRUD.getBestProducts);

// Get Product Based on User Input Price (public)
router.get('/products/price', productCRUD.getProductsByPrice);

// Get High Rating Products (public)
router.get('/products/high-rating', productCRUD.getHighRatingProducts);

// Get Promo Products (public)
router.get('/products/promo', productCRUD.getPromoProducts);

// Get products by category ID (public)
router.get('/products/category/:categoryId', productCRUD.getProductsByCategory);

// Get products by brand ID (public)
router.get('/products/brand/:brandId', productCRUD.getProductsByBrand);

// Get product details with related products for detail page (public)
router.get('/products/:id/details', productCRUD.getProductDetails);

// Get product by ID with variants and photos (public)
router.get('/products/:id', productCRUD.getProductById);

// Create new product (admin only - strict security)
router.post('/products', strictLimiter, requireAdminStrict, productCRUD.createProduct);

// Update product (admin only - strict security)
router.put('/products/:id', strictLimiter, requireAdminStrict, productCRUD.updateProduct);

// Delete product (admin only - strict security)
router.delete('/products/:id', strictLimiter, requireAdminStrict, productCRUD.deleteProduct);

// ===== PRODUCT VARIANT ROUTES =====

// Get variants for a product (public)
router.get('/products/:productId/variants', variantCRUD.getVariantsByProductId);

// Add variant to product (admin only)
router.post('/products/:productId/variants', strictLimiter, requireAdmin, variantCRUD.createVariant);

// Bulk update variants for a product (admin only)
router.put('/products/:productId/variants', strictLimiter, requireAdmin, variantCRUD.updateProductVariants);

// Update variant (admin only)
router.put('/variants/:id', strictLimiter, requireAdmin, variantCRUD.updateVariant);

// Delete variant (admin only)
router.delete('/variants/:id', strictLimiter, requireAdmin, variantCRUD.deleteVariant);

// ===== PRODUCT PHOTO ROUTES =====

// Get photos for a product (public)
router.get('/products/:productId/photos', photoCRUD.getPhotosByProductId);

// Add photo to product (admin only)
router.post('/products/:productId/photos', strictLimiter, requireAdmin, photoCRUD.createPhoto);

// Delete photo (admin only)
router.delete('/photos/:id', strictLimiter, requireAdmin, photoCRUD.deletePhoto);

// Upload multiple photos for product (admin only)
router.post('/products/:productId/upload-photos', strictLimiter, requireAdmin, (req, res, next) => {
  req.upload.array('photos', 10)(req, res, next);
}, photoCRUD.uploadProductPhotos);

// ===== RATING ROUTES =====

// Get ratings for a product (public)
router.get('/products/:productId/ratings', ratingCRUD.getRatingsByProductId);

// Add rating to product (public - customers can leave reviews)
router.post('/products/:productId/ratings', ratingLimiter, validateProductId, validateRating, handleValidationErrors, ratingCRUD.createRating);

// ===== WEB BANNER ROUTES =====

// Get all web banners (public)
router.get('/banners', bannerCRUD.getAllBanners);

// Get active web banners only (public)
router.get('/banners/active', bannerCRUD.getActiveBanners);

// Create new banner (admin only)
router.post('/banners', strictLimiter, requireAdmin, validateBanner, handleValidationErrors, bannerCRUD.createBanner);

// Update banner (admin only)
router.put('/banners/:id', strictLimiter, requireAdmin, validateId, validateBanner, handleValidationErrors, bannerCRUD.updateBanner);

// Activate banner (admin only)
router.patch('/banners/:id/activate', strictLimiter, requireAdmin, validateId, handleValidationErrors, bannerCRUD.activateBanner);

// Deactivate banner (admin only)
router.patch('/banners/:id/deactivate', strictLimiter, requireAdmin, validateId, handleValidationErrors, bannerCRUD.deactivateBanner);

// Delete banner (admin only)
router.delete('/banners/:id', strictLimiter, requireAdmin, validateId, handleValidationErrors, bannerCRUD.deleteBanner);

// Upload banner images (admin only)
router.post('/banners/:id/upload-images', strictLimiter, requireAdmin, (req, res, next) => {
  req.upload.array('images', 2)(req, res, next);
}, bannerCRUD.uploadBannerImages);

module.exports = router;